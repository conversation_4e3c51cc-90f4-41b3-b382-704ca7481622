"""
Example usage of the new proxy modes in Amazon Scraper.
"""

from amazon_scraper.scraper import AmazonScraper
from amazon_scraper.collector import AmazonDataCollector
from amazon_scraper import scrape_amazon_direct

def example_direct_scraper_usage():
    """Examples using the scraper directly"""
    
    print("=== DIRECT SCRAPER USAGE EXAMPLES ===\n")
    
    # Example 1: Fast Proxy Mode (Recommended)
    print("1. Fast Proxy Mode (Recommended - Chrome built-in proxy):")
    scraper = AmazonScraper(use_proxy=True, advanced_proxy=False)
    print("   Created scraper with fast proxy mode")
    print("   Features: Chrome proxy, fast parsing, batch price checking\n")
    
    # Example 2: Advanced Proxy Mode (Full features)
    print("2. Advanced Proxy Mode (selenium-wire with full features):")
    scraper = AmazonScraper(use_proxy=True, advanced_proxy=True)
    print("   Created scraper with advanced proxy mode")
    print("   Features: selenium-wire, request interception, individual price checking\n")
    
    # Example 3: No Proxy Mode (Fastest but risky)
    print("3. No Proxy Mode (Fastest but may get blocked):")
    scraper = AmazonScraper(use_proxy=False)
    print("   Created scraper with no proxy")
    print("   Features: Maximum speed, no proxy protection\n")

def example_collector_usage():
    """Examples using the data collector"""
    
    print("=== DATA COLLECTOR USAGE EXAMPLES ===\n")
    
    # Example 1: Fast Proxy Mode (Default)
    print("1. Fast Proxy Mode (Default):")
    collector = AmazonDataCollector(max_pages=1, use_proxy=True, advanced_proxy=False)
    print("   Created collector with fast proxy mode")
    print("   Usage: collector.collect_amazon_product_data(url)\n")
    
    # Example 2: Advanced Proxy Mode
    print("2. Advanced Proxy Mode:")
    collector = AmazonDataCollector(max_pages=1, use_proxy=True, advanced_proxy=True)
    print("   Created collector with advanced proxy mode")
    print("   Usage: collector.collect_amazon_product_data(url)\n")
    
    # Example 3: No Proxy Mode
    print("3. No Proxy Mode:")
    collector = AmazonDataCollector(max_pages=1, use_proxy=False)
    print("   Created collector with no proxy")
    print("   Usage: collector.collect_amazon_product_data(url)\n")

def example_cli_usage():
    """Examples using the CLI interface"""
    
    print("=== CLI USAGE EXAMPLES ===\n")
    
    print("1. Fast Proxy Mode (Default):")
    print("   python -m amazon_scraper --category electronics")
    print("   # Uses fast proxy mode by default\n")
    
    print("2. Advanced Proxy Mode:")
    print("   python -m amazon_scraper --category electronics --advanced-proxy")
    print("   # Uses selenium-wire with full proxy features\n")
    
    print("3. No Proxy Mode:")
    print("   python -m amazon_scraper --category electronics --no-proxy")
    print("   # Disables proxy usage (fastest but risky)\n")
    
    print("4. Scrape all categories with fast proxy:")
    print("   python -m amazon_scraper --all --max-pages 2")
    print("   # Uses fast proxy mode for all categories\n")

def example_programmatic_usage():
    """Examples using the programmatic interface"""
    
    print("=== PROGRAMMATIC USAGE EXAMPLES ===\n")
    
    print("1. Fast Proxy Mode (Recommended):")
    print("   scrape_amazon_direct(category='electronics', use_proxy=True, advanced_proxy=False)")
    print("   # Best balance of speed and protection\n")
    
    print("2. Advanced Proxy Mode:")
    print("   scrape_amazon_direct(category='electronics', use_proxy=True, advanced_proxy=True)")
    print("   # Full selenium-wire features\n")
    
    print("3. No Proxy Mode:")
    print("   scrape_amazon_direct(category='electronics', use_proxy=False)")
    print("   # Maximum speed, no proxy protection\n")

def main():
    """Run all examples"""
    print("AMAZON SCRAPER - PROXY MODE USAGE EXAMPLES")
    print("=" * 50)
    print()
    
    example_direct_scraper_usage()
    example_collector_usage()
    example_cli_usage()
    example_programmatic_usage()
    
    print("RECOMMENDATIONS:")
    print("=" * 20)
    print("• Use Fast Proxy Mode for best balance of speed and protection")
    print("• Use Advanced Proxy Mode only when you need selenium-wire features")
    print("• Use No Proxy Mode only for testing or when speed is critical")
    print()
    print("DEFAULT BEHAVIOR:")
    print("• All modes now use proxy by default (Fast Proxy Mode)")
    print("• This provides protection against IP blocking while maintaining good speed")

if __name__ == "__main__":
    main()
