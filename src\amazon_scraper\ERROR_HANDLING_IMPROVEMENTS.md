# Amazon Scraper Error Handling Improvements

## Overview

This document outlines the comprehensive error handling improvements made to the Amazon scraper to better handle proxy failures and provide more informative error messages.

## Key Improvements

### 1. Enhanced Error Classes

#### New Error Types
- **`ProxyError`**: Base class for all proxy-related errors
- **`ProxyValidationError`**: Specific proxy validation failures with detailed reasons
- **`NoWorkingProxyError`**: When no working proxies are found after testing
- **`AmazonBlockingError`**: When Amazon is actively blocking requests (CAPTCHA, robot checks, etc.)
- **`PageScrapingError`**: Page-specific scraping failures with context (URL, title, status)

#### Enhanced Error Context
All new error classes include detailed context:
- Which proxy failed and why
- Number of proxies tested
- Page URLs and titles when scraping fails
- HTTP status codes when available

### 2. Proxy Health Tracking

#### `ProxyHealthTracker` Class
- **Failure Tracking**: Records failure count for each proxy
- **Blacklisting**: Automatically blacklists proxies after 3 failures
- **Success Recording**: Resets failure count on successful connections
- **Smart Filtering**: Avoids testing known bad proxies

#### Benefits
- Reduces time spent on known bad proxies
- Improves overall scraping performance
- Provides insights into proxy reliability

### 3. Enhanced Proxy Validation

#### Detailed Validation Process
- **Comprehensive Error Handling**: Catches specific request exceptions
- **Detailed Logging**: Records exact failure reasons for each proxy
- **Health Integration**: Updates proxy health tracker automatically
- **Multiple Test Endpoints**: Uses reliable test endpoints for validation

#### Validation Features
- Connection timeout detection
- Proxy connection error identification
- HTTP status code validation
- Request exception categorization

### 4. Intelligent Fallback Strategies

#### Multi-Level Fallback
1. **Primary**: Use configured proxy mode (fast/advanced)
2. **Secondary**: Fall back to alternative proxy mode
3. **Tertiary**: Fall back to no-proxy mode
4. **Quaternary**: Retry with different proxies

#### Retry Mechanisms
- **Amazon Blocking**: 3-tier retry strategy with increasing delays
- **Page Timeouts**: Simple retry with random delays
- **Driver Failures**: Automatic driver reinitialization
- **Proxy Failures**: Automatic proxy switching

### 5. Better Error Detection

#### Amazon Blocking Detection
Enhanced detection for various blocking scenarios:
- CAPTCHA pages
- Robot check pages
- Character verification
- Error alerts
- 404/503 errors
- Generic error pages

#### Page Analysis
- **Multiple Selectors**: Tests various product selectors
- **Page Source Analysis**: Logs page content for debugging
- **Title Extraction**: Includes page titles in error messages
- **Empty Results Detection**: Distinguishes between no products and blocking

### 6. Improved Logging and Diagnostics

#### Structured Logging
- **Error Classification**: Clear categorization of error types
- **Context Information**: Detailed context for each error
- **Failure Tracking**: Comprehensive failure reason logging
- **Success Metrics**: Detailed success reporting

#### Debug Information
- Proxy validation details
- Page loading progress
- Element detection results
- Retry attempt tracking

## Usage Examples

### Basic Usage with Enhanced Error Handling

```python
from amazon_scraper.scraper import AmazonScraper, NoWorkingProxyError, AmazonBlockingError

# Initialize with enhanced error handling
scraper = AmazonScraper(use_proxy=True, advanced_proxy=False)

try:
    products = scraper.scrape_amazon_page(url, max_pages=5)
except NoWorkingProxyError as e:
    print(f"All proxies failed: {e}")
    # Handle proxy failure (maybe switch to no-proxy mode)
except AmazonBlockingError as e:
    print(f"Amazon is blocking requests: {e}")
    # Handle blocking (maybe wait longer or change strategy)
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Proxy Health Monitoring

```python
# Check proxy health after scraping
scraper = AmazonScraper()
print(f"Blacklisted proxies: {len(scraper.proxy_health.proxy_blacklist)}")
for proxy, failures in scraper.proxy_health.proxy_failures.items():
    print(f"Proxy {proxy}: {failures} failures")
```

## Error Handling Flow

### 1. Proxy Initialization
```
Try primary proxy method
├── Success: Continue with scraping
├── NoWorkingProxyError: Try fallback method
│   ├── Success: Continue with scraping
│   └── Failure: Fall back to no-proxy mode
└── Other Error: Log and retry
```

### 2. Page Scraping
```
Load page
├── Success: Parse products
├── AmazonBlockingError: Apply retry strategies
│   ├── Strategy 1: Wait and retry
│   ├── Strategy 2: Change proxy
│   └── Strategy 3: Switch to no-proxy
├── PageScrapingError: Simple retry or skip
└── Other Error: Driver recovery or skip
```

### 3. Error Recovery
```
Error detected
├── Classify error type
├── Apply appropriate recovery strategy
├── Log detailed context
├── Update proxy health if applicable
└── Continue or fail gracefully
```

## Configuration Options

### Retry Settings
- `max_retries`: Maximum retry attempts (default: 3)
- `retry_count`: Current retry count (auto-managed)

### Proxy Health Settings
- Blacklist threshold: 3 failures (configurable in `ProxyHealthTracker`)
- Success reset: Automatic on successful connection

### Logging Levels
- `DEBUG`: Detailed proxy validation and page analysis
- `INFO`: Success/failure summaries and retry attempts
- `WARNING`: Fallback activations and recoverable errors
- `ERROR`: Unrecoverable errors and final failures

## Testing

Run the error handling test suite:

```bash
cd src/amazon_scraper
python test_error_handling.py
```

This will test:
- Proxy validation and health tracking
- Fallback mechanisms
- Driver initialization with error handling
- Error classification and handling

## Benefits

1. **Reliability**: Better handling of proxy failures and Amazon blocking
2. **Performance**: Faster proxy selection through health tracking
3. **Debugging**: Detailed error context for troubleshooting
4. **Resilience**: Multiple fallback strategies for different failure modes
5. **Monitoring**: Clear visibility into proxy and scraping health

## Future Enhancements

- Proxy rotation strategies
- Dynamic proxy pool management
- Machine learning-based blocking detection
- Automated proxy quality scoring
- Real-time proxy health monitoring dashboard
