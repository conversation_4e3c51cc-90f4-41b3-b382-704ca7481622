import logging
import time
import random
import concurrent.futures
from enum import Enum
from typing import List
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import os
from datetime import datetime
import requests
from urllib.parse import quote
import re

from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement
from selenium import webdriver  # Use regular selenium by default
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from amazon_scraper.models import Product
from amazon_scraper.db_helper import DatabaseHelper
from .proxy_config import PROXY_LIST
logging.getLogger().setLevel(logging.ERROR)

formatter = logging.Formatter('%(levelname)s - %(message)s')
handler = logging.StreamHandler()
handler.setFormatter(formatter)

price_logger = logging.getLogger('amazon_scraper.price_parser')
price_logger.setLevel(logging.INFO)
price_logger.addHandler(handler)

logging.getLogger("WDM").setLevel(logging.ERROR)
logging.getLogger("seleniumwire").setLevel(logging.ERROR)
logging.getLogger("urllib3").setLevel(logging.ERROR)

class DriverInitializationError(BaseException):
    message = "Unable to initialize Chrome webdriver for scraping."

class DriverGetProductsError(BaseException):
    message = "Unable to get Amazon product data with Chrome webdriver."

class MissingProductDataError(BaseException):
    message = "Missing required data for product."

class ProductXPath(str, Enum):
    PRODUCTS = "//div[@data-component-type='s-search-result']"
    TITLE = ".//a/h2/span"
    URL = ".//a/h2"
    PRICE_FULL = ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]"
    NEXT_PAGE = "//a[contains(@class,'s-pagination-next')]"

class AmazonScraper:
    """Class for scraping Amazon"""

    def __init__(self, logger: logging.Logger | None = None, use_proxy: bool = True, advanced_proxy: bool = False) -> None:
            self._logger = logger if logger else logging.getLogger(__name__)
            self.db = DatabaseHelper()  # Initialize database helper
            self.use_proxy = use_proxy  # Always use proxy by default
            self.advanced_proxy = advanced_proxy  # Use selenium-wire for advanced proxy features

    def _get_random_user_agent(self) -> str:
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Edge/*********",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
        ]
        return random.choice(user_agents)

    def _add_headers_to_request(self, request) -> None:
        """Intercepts selenium requests to add randomized headers"""
        headers = {
            "User-Agent": self._get_random_user_agent(),
            "Accept-Language": random.choice(["en-US,en;q=0.9", "it-IT,it;q=0.9"]),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Connection": "keep-alive",
            "Referer": "https://www.amazon.it/",
            "Host": "www.amazon.it",
            "TE": "Trailers",
        }
        for key, value in headers.items():
            request.headers[key] = value

    def _init_chrome_driver(self):
        """Initializes Chrome webdriver - always with proxy, different implementations for speed vs features"""
        chrome_options = Options()

        # Optimized headless settings for speed
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-dev-shm-usage")

        # Ultra-aggressive performance options for maximum speed
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-features=TranslateUI")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--aggressive-cache-discard")

        # Add experimental options
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)

        service = Service(r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe")
        service.creation_flags = 0x08000000

        try:
            if not self.use_proxy:
                # No proxy mode (kept for backward compatibility)
                driver = webdriver.Chrome(service=service, options=chrome_options)
                self._logger.info("Using regular webdriver (no proxy)")
            elif self.advanced_proxy:
                # Advanced proxy mode using selenium-wire (slower but more features)
                from seleniumwire import webdriver as seleniumwire_webdriver

                proxy = self._get_working_proxy_for_seleniumwire()
                if proxy:
                    seleniumwire_options = {
                        'proxy': proxy,
                        'verify_ssl': False,
                        'suppress_connection_errors': True,
                        'connection_timeout': 15,
                        'read_timeout': 15,
                    }
                    driver = seleniumwire_webdriver.Chrome(
                        service=service,
                        options=chrome_options,
                        seleniumwire_options=seleniumwire_options
                    )
                    self._logger.info(f"Using advanced proxy (selenium-wire): {proxy}")
                    # Add request interceptor for selenium-wire
                    driver.request_interceptor = self._add_headers_to_request
                else:
                    self._logger.warning("No working proxy found for selenium-wire, falling back to Chrome proxy")
                    # Fall back to Chrome proxy
                    proxy_address = self._get_working_proxy_address()
                    if proxy_address:
                        chrome_options.add_argument(f"--proxy-server=http://{proxy_address}")
                        driver = webdriver.Chrome(service=service, options=chrome_options)
                        self._logger.info(f"Using Chrome proxy fallback: {proxy_address}")
                    else:
                        raise DriverInitializationError("No working proxies found")
            else:
                # Fast proxy mode using Chrome's built-in proxy (faster than selenium-wire)
                proxy_address = self._get_working_proxy_address()
                if proxy_address:
                    chrome_options.add_argument(f"--proxy-server=http://{proxy_address}")
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                    self._logger.info(f"Using fast Chrome proxy: {proxy_address}")
                else:
                    self._logger.warning("No working proxy found, falling back to no proxy")
                    driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set ultra-fast timeouts for maximum speed
            driver.set_page_load_timeout(10)  # Reduced from 20 to 10
            driver.set_script_timeout(8)      # Reduced from 15 to 8
            driver.implicitly_wait(2)         # Reduced from 3 to 2

            # Test driver functionality
            driver.execute_script("return navigator.userAgent;")

            return driver

        except Exception as e:
            self._logger.error(f"Failed to initialize Chrome driver: {str(e)}")
            raise DriverInitializationError("Failed to initialize Chrome driver") from e

    def _parse_price_for_product_fast(self, product: WebElement) -> str | None:
        """Fast price parsing - only try the most common selectors"""
        try:
            # Try only the most common price selector first
            price_elements = product.find_elements(By.XPATH, ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]")
            if price_elements:
                price_text = price_elements[0].text.strip()
                if price_text:
                    # Quick price formatting
                    cleaned = price_text.replace("€", "").replace("$", "").replace(" ", "").strip()
                    try:
                        if "," in cleaned:
                            price_float = float(cleaned.replace(".", "").replace(",", "."))
                        else:
                            price_float = float(cleaned)

                        if 0.01 <= price_float <= 100000:
                            return f"{price_float:.2f}".replace(".", ",")
                    except:
                        pass

            # Quick fallback - try price-whole
            price_elements = product.find_elements(By.XPATH, ".//span[contains(@class, 'a-price-whole')]")
            if price_elements:
                price_text = price_elements[0].text.strip()
                if price_text:
                    try:
                        price_float = float(price_text.replace(".", "").replace(",", "."))
                        if 0.01 <= price_float <= 100000:
                            return f"{price_float:.2f}".replace(".", ",")
                    except:
                        pass

        except:
            pass

        return None

    def _parse_product_data_fast(self, product: WebElement) -> Product:
        """Fast product parsing - minimal XPath attempts"""
        # Get ASIN first (fastest)
        asin_code = product.get_attribute("data-asin")
        if not asin_code:
            raise MissingProductDataError("No ASIN")

        # Try only the most common title selector
        title_elems = product.find_elements(By.XPATH, ".//h2//span")
        if not title_elems:
            title_elems = product.find_elements(By.XPATH, ".//a/h2/span")

        if not title_elems:
            raise MissingProductDataError("No title")

        title = title_elems[0].text.strip()
        if not title:
            raise MissingProductDataError("Empty title")

        # Try only the most common URL selector
        url_elems = product.find_elements(By.XPATH, ".//h2/a")
        if not url_elems:
            url_elems = product.find_elements(By.XPATH, ".//a[contains(@class, 'a-link-normal')]")

        if not url_elems:
            raise MissingProductDataError("No URL")

        url = url_elems[0].get_attribute("href")
        if not url:
            raise MissingProductDataError("Empty URL")

        # Fast price parsing
        price = self._parse_price_for_product_fast(product)
        if not price:
            raise MissingProductDataError("No price")

        return Product(title=title, url=url, asin_code=asin_code, price=price)

    def _parse_product_data(self, product: WebElement) -> Product:
        """Use fast parsing by default"""
        return self._parse_product_data_fast(product)

    def _get_products_from_page(self, url: str, driver) -> List[Product]:
        """Fast page scraping - optimized for speed"""
        try:
            # Quick cleanup
            try:
                driver.delete_all_cookies()
                if hasattr(driver, 'requests'):
                    del driver.requests
            except:
                pass

            # Fast page loading
            self._logger.debug(f"Loading page: {url}")
            driver.get(url)

            # Ultra-quick page check
            try:
                WebDriverWait(driver, 4).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            except:
                self._logger.warning("Page load timeout, continuing...")

            # Quick captcha check
            if driver.find_elements(By.XPATH, "//form[contains(@action, 'validateCaptcha')]"):
                self._logger.warning("Captcha detected!")
                raise Exception("Captcha detected")

            # Minimal scrolling for speed
            try:
                driver.execute_script("window.scrollTo(0, 1500);")
                time.sleep(0.2)  # Very quick
            except:
                pass

            # Fast product finding
            product_elements = driver.find_elements(By.XPATH, ProductXPath.PRODUCTS)

            if not product_elements:
                # Quick fallback only
                product_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 's-result-item')]")

            if not product_elements:
                raise NoSuchElementException("No products found")

            # Fast parallel processing with more workers
            parsed_products = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_product = {
                    executor.submit(self._parse_product_data, product): product
                    for product in product_elements
                }

                for future in concurrent.futures.as_completed(future_to_product):
                    try:
                        parsed_product = future.result()
                        parsed_products.append(parsed_product)
                    except MissingProductDataError:
                        continue
                    except Exception:
                        continue

            if parsed_products:
                self._logger.info(f"Successfully parsed {len(parsed_products)} products")
                return parsed_products
            else:
                raise NoSuchElementException("No valid products could be parsed")

        except Exception as e:
            self._logger.warning(f"Page scraping failed: {str(e)}")
            raise

    def scrape_amazon_page(self, url: str, csv_path: str = None, max_pages: int = 20) -> List[Product]:
        """
        Retrieves a list of products from Amazon and compares with existing prices.

        Args:
            url (str): The starting Amazon page URL
            csv_path (str): Path to CSV file with existing prices
            max_pages (int): Maximum number of pages to scrape (default: 4)

        Returns:
            List[Product]: A list of Product objects with price comparison data
        """
        self._logger.info(f"Scraping Amazon product data (up to {max_pages} pages)...")

        driver = None
        try:
            driver = self._init_chrome_driver()
        except Exception as e:
            self._logger.error(f"Failed to initialize driver: {str(e)}")
            raise DriverInitializationError from e

        all_products = []
        current_url = url
        current_page = 1

        try:
            while current_url and current_page <= max_pages:
                self._logger.info(f"Scraping page {current_page}...")

                try:
                    products = self._get_products_from_page(current_url, driver)
                except Exception as e:
                    self._logger.error(f"Failed to scrape page {current_page}: {str(e)}")

                    # Try to reinitialize driver if it seems to be dead
                    if "target window already closed" in str(e).lower() or "session" in str(e).lower():
                        self._logger.warning("Driver appears to be dead, attempting to reinitialize...")
                        try:
                            driver.quit()
                        except:
                            pass

                        try:
                            driver = self._init_chrome_driver()
                            products = self._get_products_from_page(current_url, driver)
                        except Exception as reinit_error:
                            self._logger.error(f"Driver reinitialization failed: {str(reinit_error)}")
                            break
                    else:
                        # For other errors, skip this page and continue
                        current_page += 1
                        continue

                # Handle price checking based on proxy mode
                if not self.use_proxy:  # No proxy mode - skip individual price checks for speed
                    pass  # Skip individual price checks for speed
                elif self.advanced_proxy:  # Advanced proxy mode - do individual price checks
                    # Compare prices and add price change information
                    for product in products:
                        try:
                            self._check_price_drop(product)
                        except Exception as e:
                            self._logger.warning(f"Error checking price drop for product {product.asin_code}: {str(e)}")
                else:  # Fast proxy mode - skip individual checks, do batch at end
                    pass  # Skip individual price checks for speed

                all_products.extend(products)
                self._logger.info(f"Found {len(products)} products on page {current_page}")

                # Clear request queue before loading next page (if using selenium-wire)
                try:
                    if hasattr(driver, 'requests'):
                        del driver.requests
                except Exception as e:
                    self._logger.debug(f"Error clearing requests: {str(e)}")

                # Check for next page
                try:
                    current_url = self._has_next_page(driver)
                    if not current_url:
                        self._logger.info("No more pages available")
                        break
                except Exception as e:
                    self._logger.warning(f"Error checking for next page: {str(e)}")
                    break

                current_page += 1

                # Add delays based on proxy mode
                if current_page <= max_pages:
                    if self.advanced_proxy:
                        # Longer delays for advanced proxy mode to avoid detection
                        delay = random.uniform(1.0, 2.5)
                        self._logger.debug(f"Advanced proxy delay: {delay:.1f} seconds...")
                        time.sleep(delay)
                    elif self.use_proxy:
                        # Minimal delays for fast proxy mode
                        delay = random.uniform(0.5, 1.5)
                        self._logger.debug(f"Fast proxy delay: {delay:.1f} seconds...")
                        time.sleep(delay)
                    # No delay for no-proxy mode

            # Batch processing for modes that skip individual price checks
            if (not self.use_proxy or (self.use_proxy and not self.advanced_proxy)) and all_products:
                mode_name = "no-proxy" if not self.use_proxy else "fast-proxy"
                self._logger.info(f"Performing {mode_name} batch price checking...")
                for product in all_products:
                    try:
                        self._check_price_drop(product)
                    except Exception as e:
                        self._logger.debug(f"Error checking price drop for product {product.asin_code}: {str(e)}")

            self._logger.info(f"Total products scraped: {len(all_products)}")
            return all_products

        except Exception as e:
            self._logger.error(f"Fatal error during scraping: {str(e)}")
            raise DriverGetProductsError from e
        finally:
            if driver:
                try:
                    driver.quit()
                except Exception as e:
                    self._logger.debug(f"Error closing driver: {str(e)}")

    def _has_next_page(self, driver) -> str | None:
        """Check if there's a next page and return its URL"""
        try:
            # Try multiple selectors for next page
            for xpath in [
                "//a[contains(@class,'s-pagination-next')]",
                "//a[contains(@class,'s-pagination-item') and contains(@href,'page=')]",
                "//span[@class='s-pagination-strip']/a[contains(@href,'page=')]"
            ]:
                elements = driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and "disabled" not in element.get_attribute("class"):
                        href = element.get_attribute("href")
                        if href and "page=" in href:
                            self._logger.debug(f"Found next page URL: {href}")
                            return href

            self._logger.debug("No next page found")
            return None
        except Exception as e:
            self._logger.warning(f"Error checking for next page: {str(e)}")
            return None

    def _read_existing_prices(self, csv_path: str) -> dict:
        """
        Read existing prices from CSV file.

        Args:
            csv_path (str): Path to the CSV file

        Returns:
            dict: Dictionary with ASIN codes as keys and prices as values
        """
        if not os.path.exists(csv_path):
            return {}

        try:
            df = pd.read_csv(csv_path)
            prices = {}

            for _, row in df.iterrows():
                if pd.notna(row['price']) and pd.notna(row['asin_code']):
                    # Use the price exactly as it appears in the CSV
                    prices[row['asin_code']] = row['price']

            return prices

        except Exception as e:
            self._logger.error(f"Error reading CSV file: {e}")
            return {}

    def _send_discord_alert(self, product: Product) -> None:
        """
        Sends a Discord webhook notification for significant price drops.

        Args:
            product (Product): Product with price change information
        """
        WEBHOOK_URL = "https://discord.com/api/webhooks/1364925973933457490/LZ0pgxZkJcjPVI0taYi-jitwxsSj6oYI3i2yNgUlvD6BVuGOrri8J4IH9A3t-TCnX9nS"

        try:
            # Safely get product attributes with defaults
            title = getattr(product, 'title', 'Unknown Product')[:1024]  # Truncate to Discord limit
            old_price = getattr(product, 'old_price', 0.0)
            current_price = self.extract_price(product)
            price_change_pct = getattr(product, 'price_change_pct', 0.0)
            product_url = getattr(product, 'url', None)
            asin_code = getattr(product, 'asin_code', 'Unknown')

            # Create Discord embed with safe value handling
            embed = {
                "title": "🔥 Significant Price Drop Alert! 🔥",
                "color": 0x00ff00,
                "fields": [
                    {
                        "name": "Product",
                        "value": title if title else "Unknown Product",
                        "inline": False
                    },
                    {
                        "name": "Old Price",
                        "value": f"€{old_price:.2f}" if old_price else "N/A",
                        "inline": True
                    },
                    {
                        "name": "New Price",
                        "value": f"€{current_price:.2f}" if current_price else "N/A",
                        "inline": True
                    },
                    {
                        "name": "Price Drop",
                        "value": f"{price_change_pct:.1f}%" if price_change_pct else "N/A",
                        "inline": True
                    }
                ]
            }
            if product_url:
                embed["url"] = product_url
            payload = {
                "embeds": [embed],
                "username": "Amazon Price Alert",
                "avatar_url": "https://cdn-icons-png.flaticon.com/512/732/732241.png"
            }
            response = requests.post(WEBHOOK_URL, json=payload)
            response.raise_for_status()
            self._logger.info(f"Discord notification sent for product: {asin_code}")

        except AttributeError as e:
            self._logger.error(f"Missing product attribute: {str(e)}")
        except requests.RequestException as e:
            self._logger.error(f"Discord webhook request failed: {str(e)}")
        except Exception as e:
            self._logger.error(f"Unexpected error in Discord notification: {str(e)}")

    def extract_price(self, product: Product) -> float:
        try:
            if not product.price:
                return 0.0
            price_str = (str(product.price)
                        .replace('€', '')
                        .replace('$', '')
                        .replace(' ', '')
                        .strip())

            if ',' in price_str:
                price_str = price_str.replace('.', '').replace(',', '.')

            price_float = float(price_str)
            self._logger.debug(f"Extracted price {price_float} from {product.price}")
            return price_float

        except (ValueError, AttributeError) as e:
            self._logger.warning(f"Could not extract price from {product.price}: {str(e)}")
            return 0.0

    def _check_price_drop(self, product: Product) -> None:
        try:
            current_price = self.extract_price(product)
            #self._logger.debug(f"Checking price drop for {product.asin_code}")
            #self._logger.debug(f"Current price: {current_price}")

            if current_price <= 0:
                self._logger.warning(f"Invalid current price for product {product.title[:35]}")
                return
            price_history = self.db.get_price_history(product.asin_code)


            if price_history is not None:
                last_price, _, _ = price_history
                self._logger.debug(f"Using database last price: {last_price}")
            else:
                self._logger.info(f"New product detected: {product.asin_code}")
                self.db.save_product(product)
                return

            price_diff = current_price - last_price
            price_change_pct = ((current_price - last_price) / last_price) * 100
            product.old_price = last_price
            product.price_diff = price_diff
            product.price_change_pct = price_change_pct

            if price_change_pct <= -40.0:
                self._logger.info(
                    f"Significant price drop detected for {product.title[:35]}: "
                    f"{last_price:.2f} -> {current_price:.2f} ({price_change_pct:.1f}%)"
                )
                self._send_discord_alert(product)

            self.db.save_product(product)

        except Exception as e:
            self._logger.error(f"Error checking price drop for {product.title[:35]}: {str(e)}")
            raise

    def scrape_multiple_categories(self, category_urls: List[str], csv_path: str = None, max_pages: int = 5) -> List[Product]:
        self._logger.info(f"Scraping {len(category_urls)} categories concurrently...")

        all_products = []
        with ThreadPoolExecutor(max_workers=min(len(category_urls), 3)) as executor:
            future_to_url = {
                executor.submit(self.scrape_amazon_page, url, csv_path, max_pages): url
                for url in category_urls
            }
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    category_products = future.result()
                    self._logger.info(f"Category {url} returned {len(category_products)} products")
                    all_products.extend(category_products)
                except Exception as e:
                    self._logger.error(f"Category {url} failed with error: {str(e)}")

        self._logger.info(f"Total products scraped across all categories: {len(all_products)}")
        return all_products

    def _get_random_proxy(self) -> dict:
        """Returns a random proxy from the proxy pool"""
        proxy = random.choice(PROXY_LIST)
        return {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }

    def _validate_proxy(self, proxy: dict) -> bool:
        """Validates if proxy is working with improved timeout and error handling"""
        try:
            response = requests.get(
                'https://httpbin.org/ip',  # Use a simpler endpoint for testing
                proxies=proxy,
                timeout=5,  # Reduced timeout for faster validation
                headers={'User-Agent': self._get_random_user_agent()}
            )
            return response.status_code == 200
        except Exception as e:
            self._logger.debug(f"Proxy validation failed: {str(e)}")
            return False

    def _get_working_proxy_address(self) -> str | None:
        if not hasattr(self, '_cached_proxy_address') or not self._cached_proxy_address:
            shuffled_proxies = PROXY_LIST.copy()
            random.shuffle(shuffled_proxies)
            for proxy_address in shuffled_proxies[:3]:
                proxy = {
                    "http": f"http://{proxy_address}",
                    "https": f"http://{proxy_address}"
                }

                if self._validate_proxy_fast(proxy):
                    self._logger.debug(f"Found working proxy address: {proxy_address}")
                    self._cached_proxy_address = proxy_address
                    return proxy_address

            self._logger.warning("No working proxy addresses found in the list")
            self._cached_proxy_address = None
            return None

        return self._cached_proxy_address

    def _get_working_proxy_for_seleniumwire(self) -> dict | None:
        if not hasattr(self, '_cached_seleniumwire_proxy') or not self._cached_seleniumwire_proxy:
            shuffled_proxies = PROXY_LIST.copy()
            random.shuffle(shuffled_proxies)
            for proxy_address in shuffled_proxies[:3]:
                proxy = {
                    "http": f"http://{proxy_address}",
                    "https": f"http://{proxy_address}"
                }
                if self._validate_proxy_fast(proxy):
                    self._logger.debug(f"Found working selenium-wire proxy: {proxy_address}")
                    self._cached_seleniumwire_proxy = proxy
                    return proxy

            self._logger.warning("No working proxies found for selenium-wire")
            self._cached_seleniumwire_proxy = None
            return None

        return self._cached_seleniumwire_proxy

    def _get_working_proxy(self) -> dict | None:
        """Legacy method - returns selenium-wire proxy for backward compatibility"""
        return self._get_working_proxy_for_seleniumwire()

    def _validate_proxy_fast(self, proxy: dict) -> bool:
        """Fast proxy validation with minimal timeout"""
        try:
            response = requests.get(
                'https://httpbin.org/ip',
                proxies=proxy,
                timeout=3,  # Reduced from 5 to 3 seconds
                headers={'User-Agent': self._get_random_user_agent()}
            )
            return response.status_code == 200
        except Exception:
            return False

    def _handle_timeout_recovery(self, driver) -> bool:
        """Attempts to recover from a page timeout. Returns True if recovery successful."""
        try:
            self._logger.info("Attempting timeout recovery...")
            try:
                driver.execute_script("window.stop();")
            except:
                pass
            try:
                driver.delete_all_cookies()
                if hasattr(driver, 'requests'):
                    del driver.requests
            except:
                pass
            try:
                driver.execute_script("return document.readyState;")
                self._logger.info("Driver recovery successful")
                return True
            except:
                self._logger.warning("Driver appears to be unresponsive after recovery attempt")
                return False

        except Exception as e:
            self._logger.error(f"Recovery attempt failed: {str(e)}")
            return False