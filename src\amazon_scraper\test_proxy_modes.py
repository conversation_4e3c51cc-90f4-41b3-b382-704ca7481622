"""
Test script to verify proxy functionality in all modes.
"""

import logging
import time
from amazon_scraper.scraper import AmazonScraper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_proxy_modes():
    """Test all three proxy modes"""
    
    # Test URL - a simple Amazon search
    test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
    
    print("=" * 60)
    print("TESTING PROXY MODES")
    print("=" * 60)
    
    # Mode 1: No proxy (fastest but may get blocked)
    print("\n1. Testing NO PROXY mode...")
    try:
        start_time = time.time()
        scraper_no_proxy = AmazonScraper(use_proxy=False)
        products = scraper_no_proxy.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        print(f"   ✓ No proxy mode: Found {len(products)} products in {end_time - start_time:.2f} seconds")
    except Exception as e:
        print(f"   ✗ No proxy mode failed: {str(e)}")
    
    # Mode 2: Fast proxy (Chrome built-in proxy - fast and uses proxy)
    print("\n2. Testing FAST PROXY mode...")
    try:
        start_time = time.time()
        scraper_fast_proxy = AmazonScraper(use_proxy=True, advanced_proxy=False)
        products = scraper_fast_proxy.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        print(f"   ✓ Fast proxy mode: Found {len(products)} products in {end_time - start_time:.2f} seconds")
    except Exception as e:
        print(f"   ✗ Fast proxy mode failed: {str(e)}")
    
    # Mode 3: Advanced proxy (selenium-wire - slower but more features)
    print("\n3. Testing ADVANCED PROXY mode...")
    try:
        start_time = time.time()
        scraper_advanced_proxy = AmazonScraper(use_proxy=True, advanced_proxy=True)
        products = scraper_advanced_proxy.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        print(f"   ✓ Advanced proxy mode: Found {len(products)} products in {end_time - start_time:.2f} seconds")
    except Exception as e:
        print(f"   ✗ Advanced proxy mode failed: {str(e)}")
    
    print("\n" + "=" * 60)
    print("PROXY MODE TESTING COMPLETE")
    print("=" * 60)
    print("\nMODE DESCRIPTIONS:")
    print("• No Proxy: Fastest, no proxy protection (may get blocked)")
    print("• Fast Proxy: Uses Chrome's built-in proxy (fast + proxy protection)")
    print("• Advanced Proxy: Uses selenium-wire (slower but more proxy features)")
    print("\nRECOMMENDATION:")
    print("• Use Fast Proxy mode for best balance of speed and protection")
    print("• Use Advanced Proxy mode only if you need selenium-wire features")
    print("• Use No Proxy mode only for testing or when proxies aren't needed")

if __name__ == "__main__":
    test_proxy_modes()
