#!/usr/bin/env python3
"""
Test script to demonstrate improved error handling in Amazon scraper.
This script tests various error scenarios and recovery mechanisms.
"""

import logging
import sys
import os

# Add the parent directory to the path so we can import the scraper
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from amazon_scraper.scraper import AmazonScraper, ProxyError, NoWorkingProxyError, AmazonBlockingError, PageScrapingError

def setup_logging():
    """Setup detailed logging for testing"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('error_handling_test.log')
        ]
    )
    return logging.getLogger(__name__)

def test_proxy_validation():
    """Test proxy validation and health tracking"""
    logger = setup_logging()
    logger.info("Testing proxy validation and health tracking...")
    
    scraper = AmazonScraper(logger=logger, use_proxy=True, advanced_proxy=False)
    
    # Test proxy health tracking
    test_proxy = "1.2.3.4:8080"  # Non-existent proxy
    
    # Record some failures
    scraper.proxy_health.record_failure(test_proxy, "Connection timeout")
    scraper.proxy_health.record_failure(test_proxy, "Connection refused")
    scraper.proxy_health.record_failure(test_proxy, "HTTP 403")
    
    logger.info(f"Proxy {test_proxy} failure count: {scraper.proxy_health.get_failure_count(test_proxy)}")
    logger.info(f"Proxy {test_proxy} blacklisted: {scraper.proxy_health.is_blacklisted(test_proxy)}")
    
    return True

def test_proxy_fallback():
    """Test proxy fallback mechanisms"""
    logger = setup_logging()
    logger.info("Testing proxy fallback mechanisms...")
    
    try:
        scraper = AmazonScraper(logger=logger, use_proxy=True, advanced_proxy=False)
        
        # This should trigger proxy validation and potentially fallback
        try:
            proxy_address = scraper._get_working_proxy_address()
            logger.info(f"Successfully found working proxy: {proxy_address}")
        except NoWorkingProxyError as e:
            logger.warning(f"No working proxy found: {str(e)}")
            logger.info("This is expected if all proxies in the list are down")
            
    except Exception as e:
        logger.error(f"Unexpected error during proxy fallback test: {str(e)}")
        return False
        
    return True

def test_driver_initialization_with_fallback():
    """Test driver initialization with proxy fallback"""
    logger = setup_logging()
    logger.info("Testing driver initialization with proxy fallback...")
    
    try:
        scraper = AmazonScraper(logger=logger, use_proxy=True, advanced_proxy=False)
        
        # This should attempt to initialize with proxy and fallback if needed
        try:
            driver = scraper._init_chrome_driver()
            logger.info("Driver initialized successfully")
            
            # Test basic functionality
            driver.get("https://www.google.com")
            title = driver.title
            logger.info(f"Test page loaded successfully: {title}")
            
            driver.quit()
            return True
            
        except Exception as e:
            logger.error(f"Driver initialization failed: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"Unexpected error during driver initialization test: {str(e)}")
        return False

def test_error_classification():
    """Test error classification and handling"""
    logger = setup_logging()
    logger.info("Testing error classification...")
    
    # Test different error types
    try:
        # Test ProxyError
        raise NoWorkingProxyError(5)
    except NoWorkingProxyError as e:
        logger.info(f"NoWorkingProxyError caught correctly: {str(e)}")
    
    try:
        # Test AmazonBlockingError
        raise AmazonBlockingError("CAPTCHA detected", "https://amazon.com/test")
    except AmazonBlockingError as e:
        logger.info(f"AmazonBlockingError caught correctly: {str(e)}")
    
    try:
        # Test PageScrapingError
        raise PageScrapingError("No products found", "https://amazon.com/test", "Test Page", 200)
    except PageScrapingError as e:
        logger.info(f"PageScrapingError caught correctly: {str(e)}")
    
    return True

def main():
    """Run all error handling tests"""
    logger = setup_logging()
    logger.info("Starting Amazon scraper error handling tests...")
    
    tests = [
        ("Proxy Validation", test_proxy_validation),
        ("Proxy Fallback", test_proxy_fallback),
        ("Driver Initialization", test_driver_initialization_with_fallback),
        ("Error Classification", test_error_classification),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            status = "PASSED" if result else "FAILED"
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("All tests passed! Error handling improvements are working correctly.")
    else:
        logger.warning("Some tests failed. Check the logs for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
