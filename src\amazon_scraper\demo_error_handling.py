#!/usr/bin/env python3
"""
Demonstration script showing the improved error handling in the Amazon scraper.
This script shows how the new error handling provides better information and recovery.
"""

import logging
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from amazon_scraper.scraper import (
    AmazonScraper,
    ProxyHealthTracker,
    NoWorkingProxyError,
    AmazonBlockingError,
    PageScrapingError,
    ProxyValidationError
)

def setup_demo_logging():
    """Setup logging for the demonstration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s:%(name)s - %(message)s'
    )
    return logging.getLogger(__name__)

def demo_proxy_health_tracking():
    """Demonstrate proxy health tracking features"""
    print("\n" + "="*60)
    print("DEMO: Proxy Health Tracking")
    print("="*60)

    # Create a proxy health tracker
    health_tracker = ProxyHealthTracker()

    # Simulate some proxy failures
    test_proxies = ["*******:8080", "*******:3128", "9.10.11.12:8080"]

    for proxy in test_proxies:
        print(f"\nTesting proxy: {proxy}")

        # Simulate failures
        health_tracker.record_failure(proxy, "Connection timeout")
        print(f"  Failure 1 recorded - Count: {health_tracker.get_failure_count(proxy)}")

        health_tracker.record_failure(proxy, "HTTP 403 Forbidden")
        print(f"  Failure 2 recorded - Count: {health_tracker.get_failure_count(proxy)}")

        health_tracker.record_failure(proxy, "Connection refused")
        print(f"  Failure 3 recorded - Count: {health_tracker.get_failure_count(proxy)}")

        print(f"  Blacklisted: {health_tracker.is_blacklisted(proxy)}")

    print(f"\nTotal blacklisted proxies: {len(health_tracker.proxy_blacklist)}")
    print(f"Blacklisted proxies: {list(health_tracker.proxy_blacklist)}")

def demo_error_classification():
    """Demonstrate the new error classification system"""
    print("\n" + "="*60)
    print("DEMO: Error Classification")
    print("="*60)

    # Demonstrate different error types
    error_examples = [
        (NoWorkingProxyError(5), "No working proxies found after testing 5 proxies"),
        (AmazonBlockingError("CAPTCHA detected", "https://amazon.com/test"), "Amazon blocking with CAPTCHA"),
        (PageScrapingError("No products found", "https://amazon.com/search", "Amazon Search", 200), "Page scraping failure"),
        (ProxyValidationError("*******:8080", "Connection timeout"), "Proxy validation failure")
    ]

    for error, description in error_examples:
        print(f"\n{description}:")
        print(f"  Error Type: {type(error).__name__}")
        print(f"  Error Message: {str(error)}")

        # Show error-specific attributes
        if hasattr(error, 'attempted_proxies'):
            print(f"  Attempted Proxies: {error.attempted_proxies}")
        if hasattr(error, 'url'):
            print(f"  URL: {error.url}")
        if hasattr(error, 'proxy_address'):
            print(f"  Proxy Address: {error.proxy_address}")
        if hasattr(error, 'reason'):
            print(f"  Failure Reason: {error.reason}")

def demo_enhanced_logging():
    """Demonstrate enhanced logging capabilities"""
    print("\n" + "="*60)
    print("DEMO: Enhanced Logging")
    print("="*60)

    logger = setup_demo_logging()

    # Create scraper with enhanced logging
    scraper = AmazonScraper(logger=logger, use_proxy=True, advanced_proxy=False)

    print("\nThe scraper now provides detailed logging for:")
    print("  ✓ Proxy validation attempts and results")
    print("  ✓ Fallback strategy activations")
    print("  ✓ Error classification and context")
    print("  ✓ Retry attempt tracking")
    print("  ✓ Page analysis and blocking detection")

    print(f"\nProxy health tracker initialized:")
    print(f"  - Failure tracking: {len(scraper.proxy_health.proxy_failures)} proxies tracked")
    print(f"  - Blacklist: {len(scraper.proxy_health.proxy_blacklist)} proxies blacklisted")
    print(f"  - Success tracking: {len(scraper.proxy_health.proxy_last_success)} proxies with success records")

def demo_fallback_strategies():
    """Demonstrate fallback strategies"""
    print("\n" + "="*60)
    print("DEMO: Fallback Strategies")
    print("="*60)

    print("The scraper now implements multiple fallback strategies:")

    print("\n1. Proxy Initialization Fallback:")
    print("   Primary: Advanced proxy (selenium-wire)")
    print("   ↓ (if fails)")
    print("   Secondary: Fast proxy (Chrome built-in)")
    print("   ↓ (if fails)")
    print("   Tertiary: No proxy mode")

    print("\n2. Amazon Blocking Recovery:")
    print("   Strategy 1: Wait 10-20 seconds and retry")
    print("   ↓ (if still blocked)")
    print("   Strategy 2: Switch to different proxy")
    print("   ↓ (if still blocked)")
    print("   Strategy 3: Switch to no-proxy mode")

    print("\n3. Page Scraping Recovery:")
    print("   Timeout: Retry with random delay")
    print("   Driver failure: Reinitialize driver")
    print("   Parse failure: Skip page and continue")

def demo_blocking_detection():
    """Demonstrate Amazon blocking detection"""
    print("\n" + "="*60)
    print("DEMO: Amazon Blocking Detection")
    print("="*60)

    blocking_indicators = [
        "CAPTCHA detected",
        "Robot check page",
        "Character verification",
        "Error alert on page",
        "Verification required",
        "Generic error page",
        "404 error",
        "503 error"
    ]

    print("The scraper now detects various Amazon blocking scenarios:")
    for i, indicator in enumerate(blocking_indicators, 1):
        print(f"  {i}. {indicator}")

    print("\nWhen blocking is detected:")
    print("  ✓ Specific error type (AmazonBlockingError) is raised")
    print("  ✓ Detailed context (URL, page title) is provided")
    print("  ✓ Appropriate recovery strategy is applied")
    print("  ✓ Retry attempts are logged and tracked")

def main():
    """Run all demonstrations"""
    print("Amazon Scraper Error Handling Improvements")
    print("=" * 60)
    print("This demonstration shows the enhanced error handling capabilities")
    print("that have been added to make the scraper more robust and informative.")

    # Run all demonstrations
    demo_proxy_health_tracking()
    demo_error_classification()
    demo_enhanced_logging()
    demo_fallback_strategies()
    demo_blocking_detection()

    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print("The Amazon scraper now includes:")
    print("  ✓ Comprehensive error classification")
    print("  ✓ Intelligent proxy health tracking")
    print("  ✓ Multi-level fallback strategies")
    print("  ✓ Enhanced blocking detection")
    print("  ✓ Detailed logging and diagnostics")
    print("  ✓ Automatic retry mechanisms")
    print("  ✓ Graceful error recovery")

    print("\nThese improvements provide:")
    print("  • Better reliability when proxies fail")
    print("  • More informative error messages")
    print("  • Faster recovery from failures")
    print("  • Better debugging capabilities")
    print("  • Reduced manual intervention needed")

    print(f"\nFor detailed documentation, see: ERROR_HANDLING_IMPROVEMENTS.md")
    print(f"For testing, run: python test_error_handling.py")

if __name__ == "__main__":
    main()
